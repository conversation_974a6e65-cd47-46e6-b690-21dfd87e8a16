'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Filter } from 'lucide-react';
import SmartFilterSection from './SmartFilterSection';
import ComprehensiveFilters from './ComprehensiveFilters';

interface FilterContainerProps {
  // Basic filter data
  allEntityTypes: Array<{ id: string; name: string; [key: string]: any }>;
  allCategories: Array<{ id: string; name: string; [key: string]: any }>;
  allFeatures: Array<{ id: string; name: string; [key: string]: any }>;
  allTags: Array<{ id: string; name: string; [key: string]: any }>;

  // Selected filter IDs
  selectedEntityTypeIds: string[];
  selectedCategoryIds: string[];
  selectedFeatureIds: string[];
  selectedTagIds: string[];

  // Advanced filter values
  hasFreeTier?: boolean;
  apiAccess?: boolean;
  employeeCountRanges?: string[];
  fundingStages?: string[];
  pricingModels?: string[];
  priceRanges?: string[];
  createdAtFrom?: string;
  createdAtTo?: string;
  locationSearch?: string;
  ratingMin?: number;
  ratingMax?: number;
  reviewCountMin?: number;
  reviewCountMax?: number;
  affiliateStatus?: 'NONE' | 'APPLIED' | 'APPROVED' | 'REJECTED';
  affiliateStatusArray?: string[];
  hasAffiliateLink?: boolean;
  hasApiDocumentation?: boolean;
  integrations?: string[];
  platforms?: string[];
  targetAudience?: string[];
  status?: 'PENDING' | 'ACTIVE' | 'REJECTED' | 'INACTIVE' | 'ARCHIVED' | 'NEEDS_REVISION';

  // Entity-specific filters (flat parameters from URL)
  currentEntityFilters?: Record<string, unknown>;

  // Loading states
  isLoadingFilters?: boolean;

  // Event handlers
  onEntityTypeToggle: (id: string) => void;
  onCategoryToggle: (id: string) => void;
  onFeatureToggle: (id: string) => void;
  onTagToggle: (id: string) => void;
  onAdvancedFilterChange: (filterName: string, value: unknown) => void;
  onClearAdvancedFilters: () => void;
}

const FilterContainer: React.FC<FilterContainerProps> = ({
  // Basic filter data
  allEntityTypes,
  allCategories,
  allFeatures,
  allTags,

  // Selected filter IDs
  selectedEntityTypeIds,
  selectedCategoryIds,
  selectedFeatureIds,
  selectedTagIds,

  // Advanced filter values
  hasFreeTier,
  apiAccess,
  employeeCountRanges,
  fundingStages,
  pricingModels,
  priceRanges,
  createdAtFrom,
  createdAtTo,
  locationSearch,
  ratingMin,
  ratingMax,
  reviewCountMin,
  reviewCountMax,
  affiliateStatus,
  affiliateStatusArray,
  hasAffiliateLink,
  hasApiDocumentation,
  integrations,
  platforms,
  targetAudience,
  status,

  // Entity-specific filters
  currentEntityFilters = {},

  // Loading states
  isLoadingFilters = false,

  // Event handlers
  onEntityTypeToggle,
  onCategoryToggle,
  onFeatureToggle,
  onTagToggle,
  onAdvancedFilterChange,
  onClearAdvancedFilters,
}) => {
  const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false);

  // Calculate total active filters for mobile badge
  const totalActiveFilters = 
    selectedEntityTypeIds.length + 
    selectedCategoryIds.length + 
    selectedFeatureIds.length + 
    selectedTagIds.length;

  // Common filter sections component
  const FilterSections = () => (
    <div className="space-y-6">
      <SmartFilterSection
        title="Resource Type"
        items={allEntityTypes}
        selectedIds={selectedEntityTypeIds}
        onToggle={onEntityTypeToggle}
        isLoading={isLoadingFilters}
        defaultExpanded={true}
      />

      <SmartFilterSection
        title="Category"
        items={allCategories}
        selectedIds={selectedCategoryIds}
        onToggle={onCategoryToggle}
        isLoading={isLoadingFilters}
      />

      <SmartFilterSection
        title="Features"
        items={allFeatures}
        selectedIds={selectedFeatureIds}
        onToggle={onFeatureToggle}
        isLoading={isLoadingFilters}
      />

      <SmartFilterSection
        title="Tags"
        items={allTags}
        selectedIds={selectedTagIds}
        onToggle={onTagToggle}
        isLoading={isLoadingFilters}
      />

      <ComprehensiveFilters
        hasFreeTier={hasFreeTier}
        apiAccess={apiAccess}
        employeeCountRanges={employeeCountRanges}
        fundingStages={fundingStages}
        pricingModels={pricingModels}
        priceRanges={priceRanges}
        createdAtFrom={createdAtFrom}
        createdAtTo={createdAtTo}
        locationSearch={locationSearch}
        ratingMin={ratingMin}
        ratingMax={ratingMax}
        reviewCountMin={reviewCountMin}
        reviewCountMax={reviewCountMax}
        affiliateStatus={affiliateStatus}
        affiliateStatusArray={affiliateStatusArray}
        hasAffiliateLink={hasAffiliateLink}
        hasApiDocumentation={hasApiDocumentation}
        integrations={integrations}
        platforms={platforms}
        targetAudience={targetAudience}
        status={status}
        selectedEntityTypes={selectedEntityTypeIds}
        allEntityTypes={allEntityTypes}
        currentEntityFilters={currentEntityFilters}
        isLoading={isLoadingFilters}
        onFilterChange={onAdvancedFilterChange}
        onClearAdvanced={onClearAdvancedFilters}
      />
    </div>
  );

  return (
    <>
      {/* Mobile Filter Toggle */}
      <div className="lg:hidden mb-6">
        <Sheet open={isMobileFilterOpen} onOpenChange={setIsMobileFilterOpen}>
          <SheetTrigger asChild>
            <Button variant="outline" className="inline-flex items-center gap-2">
              <Filter className="h-4 w-4" />
              Filters
              {totalActiveFilters > 0 && (
                <Badge variant="secondary" className="ml-1">
                  {totalActiveFilters}
                </Badge>
              )}
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="w-[320px] max-w-[90vw] flex flex-col overflow-hidden">
            {/* Fixed Header */}
            <div className="flex-shrink-0 p-4 sm:p-6 pb-4 border-b border-gray-100">
              <h2 className="text-lg font-semibold text-gray-900">Filters</h2>
            </div>

            {/* Scrollable Content */}
            <div className="flex-1 overflow-y-auto filter-scrollbar p-4 sm:p-6 pt-4 space-y-6 min-w-0">
              <FilterSections />
            </div>
          </SheetContent>
        </Sheet>
      </div>

      {/* Desktop Filter Sidebar */}
      <aside className="hidden lg:block w-80 flex-shrink-0 min-w-0">
        <div className="filter-sidebar-container bg-white rounded-xl shadow-sm border border-gray-100 flex flex-col overflow-hidden">
          {/* Fixed Header */}
          <div className="flex-shrink-0 p-6 pb-4 border-b border-gray-100 bg-white rounded-t-xl relative">
            <h2 className="text-lg font-semibold text-gray-900">Filters</h2>
            {/* Subtle scroll indicator */}
            <div className="absolute bottom-0 right-6 left-6 h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent"></div>
          </div>

          {/* Scrollable Filter Content */}
          <div className="flex-1 filter-content-scroll filter-scrollbar p-6 pt-4 space-y-6 min-w-0 overflow-x-hidden">
            <FilterSections />
          </div>
        </div>
      </aside>

      {/* Overlay for mobile when filter is open */}
      {isMobileFilterOpen && (
        <div 
          className="fixed inset-0 z-30 bg-black/30 lg:hidden"
          onClick={() => setIsMobileFilterOpen(false)}
        />
      )}
    </>
  );
};

export default FilterContainer;
