'use client';

import React from 'react';
import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>R<PERSON>, Search, Sparkles, Zap, Target, <PERSON>, <PERSON>, <PERSON> } from 'lucide-react';
import Chatbot from '@/components/chatbot/Chatbot';

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      {/* Hero Section with AI Recommendation */}
      <section className="relative py-12 sm:py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-5xl mx-auto">
          {/* Hero Header */}
          <div className="text-center mb-12">
            <div className="flex items-center justify-center gap-2 mb-6">
              <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-indigo-500 to-purple-600">
                <Brain className="h-5 w-5 text-white" />
              </div>
              <span className="text-sm font-semibold text-indigo-600 uppercase tracking-wide">AI-Powered Discovery</span>
            </div>

            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
              Find Your Perfect{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600">
                AI Tool
              </span>
            </h1>

            <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8 leading-relaxed">
              Describe what you need, and our AI will instantly recommend the best tools for your specific requirements.
            </p>
          </div>

          {/* Main AI Recommendation Interface */}
          <div className="mb-16">
            <Chatbot className="max-w-4xl mx-auto" />
          </div>

          {/* Trust Indicators */}
          <div className="text-center mb-12">
            <div className="flex items-center justify-center gap-8 text-sm text-gray-500 mb-6">
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 text-yellow-500 fill-current" />
                <span>1000+ AI Tools</span>
              </div>
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4 text-indigo-500" />
                <span>Instant Results</span>
              </div>
              <div className="flex items-center gap-2">
                <Target className="h-4 w-4 text-purple-500" />
                <span>Personalized</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Secondary Actions */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white/50 backdrop-blur-sm">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Explore More Ways to Discover
            </h2>
            <p className="text-gray-600">
              Browse our curated collection or contribute to the community
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Browse All Tools */}
            <Link href="/browse" className="group">
              <div className="p-6 rounded-2xl bg-white border border-gray-200 hover:border-indigo-300 hover:shadow-lg transition-all duration-300 group-hover:-translate-y-1">
                <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-indigo-100 mb-4 group-hover:bg-indigo-200 transition-colors">
                  <Search className="h-6 w-6 text-indigo-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Browse Catalog</h3>
                <p className="text-gray-600 text-sm">Explore our complete collection of AI tools and resources</p>
              </div>
            </Link>

            {/* Submit Tool */}
            <Link href="/submit" className="group">
              <div className="p-6 rounded-2xl bg-white border border-gray-200 hover:border-purple-300 hover:shadow-lg transition-all duration-300 group-hover:-translate-y-1">
                <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-purple-100 mb-4 group-hover:bg-purple-200 transition-colors">
                  <Rocket className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Submit Tool</h3>
                <p className="text-gray-600 text-sm">Share an AI tool you've discovered with the community</p>
              </div>
            </Link>

            {/* Chat Interface */}
            <Link href="/chat" className="group">
              <div className="p-6 rounded-2xl bg-white border border-gray-200 hover:border-amber-300 hover:shadow-lg transition-all duration-300 group-hover:-translate-y-1">
                <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-amber-100 mb-4 group-hover:bg-amber-200 transition-colors">
                  <Sparkles className="h-6 w-6 text-amber-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">AI Chat</h3>
                <p className="text-gray-600 text-sm">Have a conversation with our AI assistant</p>
              </div>
            </Link>
          </div>
        </div>
      </section>

    </div>
  );
}
